import React, { useEffect, useMemo, useState } from 'react';
import Button from './common/Button';
import Input from './common/Input';
import {
  EmailOrganizationSuggestion,
  LinkedOrganization,
  PipedriveOrganization,
  PipedriveSearchResult,
} from '../types';
import { createOrganization } from '../services/pipedriveService';

interface OrganizationManagerProps {
  suggestion?: EmailOrganizationSuggestion;
  duplicates?: PipedriveSearchResult<PipedriveOrganization>;
  linkedOrganization: LinkedOrganization | null;
  onOrganizationLinked: (organization: LinkedOrganization | null) => void;
  showToast: (message: string, type: 'success' | 'error') => void;
}

const OrganizationManager: React.FC<OrganizationManagerProps> = ({
  suggestion,
  duplicates,
  linkedOrganization,
  onOrganizationLinked,
  showToast,
}) => {
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [selectedExistingId, setSelectedExistingId] = useState<number | null>(null);

  useEffect(() => {
    if (!suggestion) return;
    setName(suggestion.name ?? '');
    setAddress(suggestion.address ?? '');
  }, [suggestion]);

  useEffect(() => {
    if (selectedExistingId || !duplicates?.items?.length) return;
    const [first] = duplicates.items;
    if (first?.item?.id) {
      setSelectedExistingId(first.item.id);
    }
  }, [duplicates, selectedExistingId]);

  const selectedExisting = useMemo(() => {
    if (!duplicates || !selectedExistingId) return null;
    return duplicates.items.find((entry) => entry.item.id === selectedExistingId)?.item ?? null;
  }, [duplicates, selectedExistingId]);

  const resetForm = () => {
    setName('');
    setAddress('');
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name) {
      showToast('Organization name is required.', 'error');
      return;
    }
    setIsCreating(true);
    try {
      const response = await createOrganization({ name, address: address || undefined });
      if (!response?.success) {
        throw new Error('Unexpected response from Pipedrive.');
      }
      const organization: LinkedOrganization = {
        id: response.data.id,
        name: response.data.name ?? name,
      };
      onOrganizationLinked(organization);
      showToast('Organization created in Pipedrive.', 'success');
      resetForm();
    } catch (error: any) {
      showToast(error?.message ?? 'Failed to create organization.', 'error');
    } finally {
      setIsCreating(false);
    }
  };

  const handleLinkExisting = async () => {
    if (!selectedExisting) {
      showToast('Select an organization from the list to link.', 'error');
      return;
    }
    setIsLinking(true);
    try {
      onOrganizationLinked({ id: selectedExisting.id, name: selectedExisting.name });
      showToast('Linked to existing organization.', 'success');
    } finally {
      setIsLinking(false);
    }
  };

  return (
    <section className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm space-y-4">
      <header className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-800">Organization</h2>
        {linkedOrganization && (
          <button
            type="button"
            className="text-xs text-green-700 underline"
            onClick={() => onOrganizationLinked(null)}
          >
            Unlink
          </button>
        )}
      </header>
      <form onSubmit={handleCreate} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          id="org-name"
          label="Organization Name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="e.g., Acme Corporation"
        />
        <Input
          id="org-address"
          label="Address"
          type="text"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          placeholder="e.g., 123 Main Street, London"
        />
        <div className="md:col-span-2 flex flex-col sm:flex-row gap-2">
          <Button type="submit" isLoading={isCreating} className="sm:w-auto">
            Create Organization
          </Button>
          <Button
            type="button"
            isLoading={isLinking}
            className="sm:w-auto bg-gray-600 hover:bg-gray-700"
            onClick={handleLinkExisting}
            disabled={!selectedExisting}
          >
            Use Selected Existing Organization
          </Button>
        </div>
      </form>

      {linkedOrganization && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-sm text-green-900">
          Currently linked: <span className="font-semibold">{linkedOrganization.name}</span>
        </div>
      )}

      {duplicates?.items?.length ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <h3 className="text-sm font-semibold text-gray-700 mb-2">Potential Matches</h3>
          <div className="space-y-2">
            {duplicates.items.map(({ item, result_score }) => (
              <label
                key={item.id}
                className={`block border rounded-lg p-3 cursor-pointer transition-colors ${
                  selectedExistingId === item.id ? 'border-green-400 bg-green-50' : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    name="org-duplicate"
                    className="mt-1"
                    value={item.id}
                    checked={selectedExistingId === item.id}
                    onChange={() => setSelectedExistingId(item.id)}
                  />
                  <div className="flex-1 text-sm text-gray-700">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-800">{item.name}</span>
                      {typeof result_score === 'number' && (
                        <span className="text-xs text-gray-500">Score: {result_score.toFixed(2)}</span>
                      )}
                    </div>
                    {item.address && <p className="text-xs text-gray-600">{item.address}</p>}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>
      ) : (
        <p className="text-xs text-gray-500">No matching organizations detected.</p>
      )}
    </section>
  );
};

export default OrganizationManager;
