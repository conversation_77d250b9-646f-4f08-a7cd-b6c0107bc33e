import { apiRequest, ApiResponse } from './apiClient';
import { AIProvider, EmailAnalyzePayload, EmailAnalysisResponse } from '../types';

export const analyzeEmail = async (payload: EmailAnalyzePayload) => {
  const response = await apiRequest<ApiResponse<EmailAnalysisResponse>>('/api/email/analyze', {
    method: 'POST',
    body: payload,
  });
  return response.data;
};

export const SUPPORTED_PROVIDERS: AIProvider[] = ['gemini', 'openai', 'ollama'];
