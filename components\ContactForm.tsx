import React, { useEffect, useMemo, useState } from 'react';
import Button from './common/Button';
import Input from './common/Input';
import {
  EmailContactSuggestion,
  FormProps,
  LinkedOrganization,
  LinkedPerson,
  PipedrivePerson,
  PipedriveSearchResult,
} from '../types';
import { createContact, mergeContactInto } from '../services/pipedriveService';

interface ContactFormProps extends FormProps {
  suggestion?: EmailContactSuggestion;
  duplicates?: PipedriveSearchResult<PipedrivePerson>;
  linkedPerson: LinkedPerson | null;
  linkedOrganization: LinkedOrganization | null;
  onPersonLinked: (person: <PERSON>ed<PERSON><PERSON> | null) => void;
}

const ContactForm: React.FC<ContactFormProps> = ({
  showToast,
  suggestion,
  duplicates,
  linkedPerson,
  linkedOrganization,
  onPersonLinked,
}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [mergeTargetId, setMergeTargetId] = useState<number | null>(null);
  const [selectedExistingId, setSelectedExistingId] = useState<number | null>(null);

  useEffect(() => {
    if (!suggestion) return;
    setName(suggestion.name ?? '');
    setEmail(suggestion.email ?? '');
    setPhone(suggestion.phone ?? '');
  }, [suggestion]);

  useEffect(() => {
    if (selectedExistingId || !duplicates?.items?.length) return;
    const [first] = duplicates.items;
    if (first?.item?.id) {
      setSelectedExistingId(first.item.id);
    }
  }, [duplicates, selectedExistingId]);

  const selectedExisting = useMemo(() => {
    if (!selectedExistingId || !duplicates) return null;
    return duplicates.items.find((entry) => entry.item.id === selectedExistingId)?.item ?? null;
  }, [duplicates, selectedExistingId]);

  const resetForm = () => {
    setName('');
    setEmail('');
    setPhone('');
  };

  const handleCreate = async () => {
    if (!name || !email) {
      showToast('Name and Email are required to create a contact.', 'error');
      return;
    }
    setIsLoading(true);
    try {
      const response = await createContact({ name, email, phone, org_id: linkedOrganization?.id });
      if (!response?.success) {
        throw new Error('Unexpected response from Pipedrive.');
      }
      const data = response.data;
      const primaryEmail = Array.isArray((data as any)?.email)
        ? (data as any).email?.[0]?.value
        : (data as any)?.email ?? email;
      const linked: LinkedPerson = {
        id: data.id,
        name: data.name ?? name,
        email: primaryEmail ?? email,
      };
      onPersonLinked(linked);
      showToast('Contact created successfully in Pipedrive.', 'success');
      resetForm();
    } catch (error: any) {
      showToast(error?.message ?? 'Failed to create contact.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLinkExisting = async () => {
    if (!selectedExisting) {
      showToast('Select a contact from the duplicates list to link.', 'error');
      return;
    }
    setIsLinking(true);
    try {
      const emailValue = selectedExisting.emails?.find((entry) => entry.primary)?.value || selectedExisting.emails?.[0]?.value;
      const linked: LinkedPerson = {
        id: selectedExisting.id,
        name: selectedExisting.name,
        email: emailValue,
      };
      onPersonLinked(linked);
      showToast('Linked to existing contact.', 'success');
    } finally {
      setIsLinking(false);
    }
  };

  const handleMerge = async (primaryId: number) => {
    if (!linkedPerson) {
      showToast('Create or link a contact first before merging.', 'error');
      return;
    }
    if (linkedPerson.id === primaryId) {
      showToast('Contact already linked to this record.', 'error');
      return;
    }
    setMergeTargetId(primaryId);
    try {
      await mergeContactInto(primaryId, linkedPerson.id);
      showToast('Contacts merged in Pipedrive.', 'success');
      if (selectedExistingId === primaryId) {
        onPersonLinked({ ...linkedPerson, id: primaryId });
      }
    } catch (error: any) {
      showToast(error?.message ?? 'Failed to merge contacts.', 'error');
    } finally {
      setMergeTargetId(null);
    }
  };

  return (
    <div className="space-y-6">
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleCreate();
        }}
        className="space-y-4"
      >
        <h2 className="text-lg font-semibold text-gray-700">Create or Link Contact</h2>
        <Input
          id="contact-name"
          label="Name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="e.g., Jane Doe"
        />
        <Input
          id="contact-email"
          label="Email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="e.g., <EMAIL>"
        />
        <Input
          id="contact-phone"
          label="Phone"
          type="tel"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          placeholder="e.g., (*************"
        />
        {linkedOrganization && (
          <p className="text-xs text-gray-600">Will link to organization: {linkedOrganization.name}</p>
        )}
        <div className="pt-2 space-y-2">
          <Button type="submit" isLoading={isLoading}>
            Create New Contact
          </Button>
          <Button
            type="button"
            isLoading={isLinking}
            className="bg-gray-600 hover:bg-gray-700"
            onClick={handleLinkExisting}
            disabled={!selectedExisting}
          >
            Use Selected Existing Contact
          </Button>
        </div>
      </form>

      {linkedPerson && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-900">
          <h3 className="font-semibold mb-2">Linked Contact</h3>
          <p className="font-medium">{linkedPerson.name}</p>
          {linkedPerson.email && <p className="text-xs">{linkedPerson.email}</p>}
          <button
            type="button"
            className="mt-2 text-xs underline text-green-700"
            onClick={() => onPersonLinked(null)}
          >
            Unlink contact
          </button>
        </div>
      )}

      {duplicates?.items?.length ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Potential Duplicates</h3>
          <div className="space-y-3">
            {duplicates.items.map(({ item, result_score }) => {
              const primaryEmail = item.emails?.find((entry) => entry.primary)?.value || item.emails?.[0]?.value;
              const isSelected = selectedExistingId === item.id;
              return (
                <div
                  key={item.id}
                  className={`border rounded-lg p-3 transition-colors ${
                    isSelected ? 'border-green-400 bg-green-50' : 'border-gray-200 bg-white'
                  }`}
                >
                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="contact-duplicate"
                      className="mt-1"
                      value={item.id}
                      checked={isSelected}
                      onChange={() => setSelectedExistingId(item.id)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-800">{item.name}</span>
                        {typeof result_score === 'number' && (
                          <span className="text-xs text-gray-500">Match score: {result_score.toFixed(2)}</span>
                        )}
                      </div>
                      {primaryEmail && <p className="text-xs text-gray-600">Email: {primaryEmail}</p>}
                      {item.phones?.[0]?.value && (
                        <p className="text-xs text-gray-600">Phone: {item.phones[0].value}</p>
                      )}
                      {item.organization?.name && (
                        <p className="text-xs text-gray-600">Org: {item.organization.name}</p>
                      )}
                      {linkedPerson && (
                        <button
                          type="button"
                          className="mt-2 text-xs text-green-700 underline"
                          onClick={() => handleMerge(item.id)}
                          disabled={mergeTargetId === item.id}
                        >
                          {mergeTargetId === item.id ? 'Merging…' : 'Merge linked contact into this record'}
                        </button>
                      )}
                    </div>
                  </label>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div className="text-xs text-gray-500">No duplicates detected.</div>
      )}
    </div>
  );
};

export default ContactForm;
