{"name": "pipedrive-sync-for-outlook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "tsx server/index.ts", "dev:full": "concurrently \"npm run server\" \"npm run dev\""}, "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.12.2", "concurrently": "^9.2.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "openai": "^5.22.1", "react": "^19.1.1", "react-dom": "^19.1.1", "tsx": "^4.20.5", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^22.14.0", "@vitejs/plugin-react": "^5.0.0", "typescript": "~5.8.2", "vite": "^6.2.0"}}