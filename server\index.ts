import express from 'express';
import cors from 'cors';

// Global handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Application specific logging, throwing an error, or other logic here
  process.exit(1); // Exit with a failure code
});
import { analyzeEmail } from './emailAnalyzer';
import { config } from './config';
import {
  createDeal,
  createLead,
  createOrganization,
  createPerson,
  mergePersons,
  searchDeals,
  searchLeads,
  searchOrganizations,
  searchPersons,
} from './pipedriveClient';

const app = express();
app.use(cors());
app.use(express.json({ limit: '1mb' }));

app.get('/api/health', (_req, res) => {
  res.json({
    status: 'ok',
    provider: config.aiProvider,
    pipedriveEnabled: Boolean(config.pipedriveApiToken),
  });
});

app.post('/api/email/analyze', async (req, res) => {
  try {
    console.log(`[DEBUG /api/email/analyze] Request body: ${JSON.stringify(req.body)}`);
    const analysis = await analyzeEmail(req.body ?? {});
    const duplicates: any = {};
    if (config.pipedriveApiToken) {
      if (analysis.contact?.email) {
        duplicates.persons = await searchPersons(analysis.contact.email);
      } else if (analysis.contact?.name) {
        duplicates.persons = await searchPersons(analysis.contact.name);
      }
      if (analysis.organization?.name) {
        duplicates.organizations = await searchOrganizations(analysis.organization.name);
      }
      if (analysis.deal?.title) {
        duplicates.deals = await searchDeals(analysis.deal.title);
      }
      if (analysis.lead?.title) {
        duplicates.leads = await searchLeads(analysis.lead.title);
      }
    }
    console.log(`[DEBUG /api/email/analyze] Successfully analyzed email`);
    res.json({ data: { analysis, duplicates } });
  } catch (error: any) {
    console.log(`[DEBUG /api/email/analyze] Error: ${error?.message}`);
    console.log(`[DEBUG /api/email/analyze] Error stack: ${error?.stack}`);
    const message = error?.message ?? 'Failed to analyse email.';
    res.status(400).json({ error: message });
  }
});

app.get('/api/pipedrive/search', async (req, res) => {
  try {
    const type = String(req.query.type ?? 'person');
    const term = String(req.query.term ?? '');
    if (!term) {
      return res.status(400).json({ error: 'term query parameter is required.' });
    }
    let result;
    switch (type) {
      case 'person':
        result = await searchPersons(term);
        break;
      case 'organization':
        result = await searchOrganizations(term);
        break;
      case 'deal':
        result = await searchDeals(term);
        break;
      case 'lead':
        result = await searchLeads(term);
        break;
      default:
        return res.status(400).json({ error: `Unsupported search type: ${type}` });
    }
    res.json({ data: result });
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Search failed.';
    res.status(400).json({ error: message });
  }
});

app.post('/api/pipedrive/persons', async (req, res) => {
  try {
    const response = await createPerson(req.body);
    res.json(response);
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Failed to create person.';
    res.status(400).json({ error: message });
  }
});

app.post('/api/pipedrive/persons/:id/merge', async (req, res) => {
  try {
    const primaryId = Number(req.params.id);
    const mergeWith = Number(req.body.merge_with_id);
    if (!primaryId || !mergeWith) {
      return res.status(400).json({ error: 'primary person id and merge_with_id are required.' });
    }
    const response = await mergePersons(primaryId, mergeWith);
    res.json(response);
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Failed to merge persons.';
    res.status(400).json({ error: message });
  }
});

app.post('/api/pipedrive/organizations', async (req, res) => {
  try {
    const response = await createOrganization(req.body);
    res.json(response);
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Failed to create organization.';
    res.status(400).json({ error: message });
  }
});

app.post('/api/pipedrive/deals', async (req, res) => {
  try {
    const response = await createDeal(req.body);
    res.json(response);
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Failed to create deal.';
    res.status(400).json({ error: message });
  }
});

app.post('/api/pipedrive/leads', async (req, res) => {
  try {
    const response = await createLead(req.body);
    res.json(response);
  } catch (error: any) {
    const message = error?.response?.data ?? error?.message ?? 'Failed to create lead.';
    res.status(400).json({ error: message });
  }
});

export const startServer = () => {
  app.listen(config.port, () => {
    console.log(`Server listening on http://localhost:${config.port}`);
  });
};

if (process.env.NODE_ENV !== 'test') {
  try {
    startServer();
  } catch (error) {
    console.error('SERVER STARTUP ERROR:');
    console.error(error);
    process.exit(1); // Exit with a non-zero code to indicate failure
  }
}
