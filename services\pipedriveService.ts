import { apiRequest, ApiResponse, buildQueryString } from './apiClient';
import {
  ContactPayload,
  DealPayload,
  LeadPayload,
  PipedriveDeal,
  PipedriveLead,
  PipedriveOrganization,
  PipedrivePerson,
  PipedriveSearchResult,
} from '../types';

interface PipedriveResponse<T> {
  success: boolean;
  data: T;
  additional_data?: Record<string, unknown>;
}

const post = async <T>(path: string, payload: unknown) => {
  return apiRequest<T>(path, {
    method: 'POST',
    body: payload,
  });
};

const get = async <T>(path: string, params?: Record<string, string | number | undefined>) => {
  const query = params ? buildQueryString(params) : '';
  return apiRequest<T>(`${path}${query}`, {
    method: 'GET',
  });
};

export const searchPipedrive = async (
  type: 'person' | 'organization' | 'deal' | 'lead',
  term: string,
) => {
  const response = await get<ApiResponse<PipedriveSearchResult<any>>>(
    '/api/pipedrive/search',
    { type, term },
  );
  return response.data;
};

export const createContact = async (payload: ContactPayload) => {
  const response = await post<PipedriveResponse<PipedrivePerson>>('/api/pipedrive/persons', payload);
  return response;
};

export const mergeContactInto = async (primaryPersonId: number, mergePersonId: number) => {
  return post<PipedriveResponse<PipedrivePerson>>(`/api/pipedrive/persons/${primaryPersonId}/merge`, {
    merge_with_id: mergePersonId,
  });
};

export const createOrganization = async (payload: { name: string; address?: string }) => {
  const response = await post<PipedriveResponse<PipedriveOrganization>>('/api/pipedrive/organizations', payload);
  return response;
};

export const createDeal = async (payload: DealPayload) => {
  const response = await post<PipedriveResponse<PipedriveDeal>>('/api/pipedrive/deals', payload);
  return response;
};

export const createLead = async (payload: LeadPayload) => {
  const response = await post<PipedriveResponse<PipedriveLead>>('/api/pipedrive/leads', payload);
  return response;
};
