export enum Tab {
  CONTACT = 'contact',
  DEAL = 'deal',
  LEAD = 'lead',
}

export type AIProvider = 'openai' | 'gemini' | 'ollama';

export interface ToastMessage {
  message: string;
  type: 'success' | 'error';
}

export interface FormProps {
  showToast: (message: string, type: 'success' | 'error') => void;
}

export interface ContactPayload {
  name: string;
  email?: string;
  phone?: string;
  org_id?: number;
  owner_id?: number;
  visible_to?: number;
  label?: number;
}

export interface DealPayload {
  title: string;
  value?: number;
  currency?: string;
  person_id?: number;
  org_id?: number;
  stage_id?: number;
  pipeline_id?: number;
  expected_close_date?: string;
  probability?: number;
  notes?: string;
}

export interface LeadPayload {
  title: string;
  person_id?: number;
  org_id?: number;
  owner_id?: number;
  expected_close_date?: string;
  value?: number;
  currency?: string;
  note?: string;
  source_name?: string;
}

export interface EmailTask {
  description: string;
  dueDate?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface EmailContactSuggestion {
  name?: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  organization?: string;
  confidence: number;
}

export interface EmailOrganizationSuggestion {
  name?: string;
  address?: string;
  domain?: string;
  confidence: number;
}

export interface EmailDealSuggestion {
  title?: string;
  value?: number;
  currency?: string;
  expectedCloseDate?: string;
  pipeline?: string;
  stage?: string;
  notes?: string;
  confidence: number;
}

export interface EmailLeadSuggestion {
  title?: string;
  source?: string;
  notes?: string;
  confidence: number;
}

export interface EmailAnalysis {
  summary: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  urgency: 'low' | 'medium' | 'high';
  contact?: EmailContactSuggestion;
  organization?: EmailOrganizationSuggestion;
  deal?: EmailDealSuggestion;
  lead?: EmailLeadSuggestion;
  tasks: EmailTask[];
  keywords: string[];
  rawExtractedFields?: Record<string, unknown>;
}

export interface EmailAnalyzePayload {
  subject: string;
  body: string;
  provider?: AIProvider;
}

export interface PipedriveSearchItem<T = any> {
  result_score?: number;
  item: T;
}

export interface PipedriveSearchResult<T = any> {
  items: PipedriveSearchItem<T>[];
}

export interface PipedrivePerson {
  id: number;
  name: string;
  emails?: Array<{ value: string; primary?: boolean }>;
  phones?: Array<{ value: string; primary?: boolean }>;
  organization?: { id: number; name: string } | null;
}

export interface PipedriveOrganization {
  id: number;
  name: string;
  address?: string;
}

export interface PipedriveDeal {
  id: number;
  title: string;
  value?: number;
  currency?: string;
  org_id?: number;
  person_id?: number;
}

export interface PipedriveLead {
  id: string;
  title: string;
  person_id?: number;
  organization_id?: number;
}

export interface DuplicateMatches {
  persons?: PipedriveSearchResult<PipedrivePerson>;
  organizations?: PipedriveSearchResult<PipedriveOrganization>;
  deals?: PipedriveSearchResult<PipedriveDeal>;
  leads?: PipedriveSearchResult<PipedriveLead>;
}

export interface EmailAnalysisResponse {
  analysis: EmailAnalysis;
  duplicates: DuplicateMatches;
}

export interface LinkedPerson {
  id: number;
  name: string;
  email?: string;
}

export interface LinkedOrganization {
  id: number;
  name: string;
}

export interface ApiErrorResponse {
  error: string;
}
