<div align="center">
<img width="1200" height="475" alt="GHBanner" src="https://github.com/user-attachments/assets/0aa67016-6eaf-458a-adb2-6e31a0763ed6" />
</div>

# Pipedrive Sync for Outlook

A proof-of-concept Outlook companion that analyses incoming email with OpenAI, Gemini, or a local Ollama model, then prepares Pipedrive contacts, deals, and leads with duplicate detection and merge tooling.

## Features
- Paste any email subject and thread, then let the AI summarise intent, urgency, tasks, and CRM fields.
- Auto-populate contact, deal, and lead forms with the extracted data.
- Surface duplicate matches from Pipedrive and link or merge into existing records.
- Create or link organisations before pushing contacts and deals.
- Switch between OpenAI, Gemini, or Ollama providers by updating environment variables.

## Prerequisites
- Node.js 18+
- A Pipedrive API token with write permissions
- API access for at least one AI provider (OpenAI, Gemini, or a running Ollama instance)

## Configuration
Create a `.env.local` file (already present) and set the relevant keys:

```
VITE_SERVER_URL=http://localhost:5175

# Choose the provider you want to use (gemini, openai, or ollama)
AI_PROVIDER=gemini
OPENAI_API_KEY=YOUR_OPENAI_KEY
OPENAI_MODEL=gpt-4.1-mini
GEMINI_API_KEY=YOUR_GEMINI_KEY
GEMINI_MODEL=gemini-1.5-flash
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b-instruct-q4_K_M

PIPEDRIVE_API_TOKEN=YOUR_PIPEDRIVE_API_TOKEN
SERVER_PORT=5175
```

> The backend automatically loads both `.env` and `.env.local`, so feel free to split secrets if desired.

## Run Locally

```bash
npm install
npm run dev:full
```

This runs the Pipedrive/AI proxy server and the Vite dev server concurrently. If you prefer separate terminals:

```bash
npm run server   # backend on http://localhost:5175
npm run dev      # Vite frontend on http://localhost:5173
```

## Usage
1. Paste the subject/body of an email into the **Email Intelligence** panel and press **Analyse Email**.
2. Review the AI summary, suggested tasks, and duplicates pulled from Pipedrive.
3. (Optional) Create or link an organisation using the Organisation panel.
4. Use the Contact/Deal/Lead tabs to push the prepared data into Pipedrive or merge into existing records.

## Next Steps
- Wire into Outlook APIs to ingest emails automatically.
- Persist analysis history and user preferences.
- Add richer pipeline/stage mapping and task creation flows.

---

View the original app in AI Studio: https://ai.studio/apps/drive/1GrXPKQbPZXSMBDbLSfH6482o3lIU3rfu
