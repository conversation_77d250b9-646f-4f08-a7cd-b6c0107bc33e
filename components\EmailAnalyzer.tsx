import React, { useState } from 'react';
import Button from './common/Button';
import Input from './common/Input';
import {
  AIProvider,
  EmailAnalysis,
  EmailAnalyzePayload,
  EmailTask,
  DuplicateMatches,
} from '../types';
import { SUPPORTED_PROVIDERS } from '../services/emailService';

interface EmailAnalyzerProps {
  onAnalyze: (payload: EmailAnalyzePayload) => Promise<void>;
  isAnalyzing: boolean;
  analysis: EmailAnalysis | null;
  duplicates: DuplicateMatches | null;
}

const sentimentColors: Record<EmailAnalysis['sentiment'], string> = {
  positive: 'text-green-700 bg-green-50',
  neutral: 'text-gray-700 bg-gray-100',
  negative: 'text-red-700 bg-red-50',
};

const urgencyColors: Record<EmailAnalysis['urgency'], string> = {
  high: 'text-red-700 bg-red-50',
  medium: 'text-yellow-700 bg-yellow-100',
  low: 'text-green-700 bg-green-50',
};

const EmailAnalyzer: React.FC<EmailAnalyzerProps> = ({ onAnalyze, isAnalyzing, analysis, duplicates }) => {
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [provider, setProvider] = useState<AIProvider>('gemini');

  const handleAnalyze = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!body.trim()) {
      return;
    }
    await onAnalyze({ subject: subject || '(no subject)', body, provider });
  };

  const renderTasks = (tasks: EmailTask[]) => {
    if (!tasks.length) return <p className="text-xs text-gray-500">No tasks detected.</p>;
    return (
      <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
        {tasks.map((task, index) => (
          <li key={`${task.description}-${index}`}>
            <span className="font-medium">{task.description}</span>
            {task.dueDate && <span className="ml-2 text-xs text-gray-500">Due: {task.dueDate}</span>}
            {task.priority && <span className="ml-2 text-xs uppercase">Priority: {task.priority}</span>}
          </li>
        ))}
      </ul>
    );
  };

  const totalDuplicates =
    (duplicates?.persons?.items?.length ?? 0) +
    (duplicates?.organizations?.items?.length ?? 0) +
    (duplicates?.deals?.items?.length ?? 0) +
    (duplicates?.leads?.items?.length ?? 0);

  return (
    <section className="space-y-4 mb-6">
      <form onSubmit={handleAnalyze} className="bg-white border border-gray-200 rounded-xl p-4 space-y-4 shadow-sm">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800">Email Intelligence</h2>
          <label className="text-xs text-gray-500">
            AI Provider
            <select
              value={provider}
              onChange={(e) => setProvider(e.target.value as AIProvider)}
              className="ml-2 border border-gray-300 rounded-md text-xs px-2 py-1"
            >
              {SUPPORTED_PROVIDERS.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </label>
        </div>
        <Input
          id="email-subject"
          label="Email Subject"
          type="text"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          placeholder="Paste subject line here"
        />
        <div>
          <label htmlFor="email-body" className="block text-sm font-medium text-gray-700 mb-1">
            Email Body
          </label>
          <textarea
            id="email-body"
            value={body}
            onChange={(e) => setBody(e.target.value)}
            rows={8}
            placeholder="Paste the email thread here to analyse for CRM updates"
            className="block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm transition-colors"
          />
        </div>
        <div className="pt-2">
          <Button type="submit" isLoading={isAnalyzing} disabled={!body.trim()}>
            Analyse Email
          </Button>
        </div>
      </form>

      {analysis && (
        <div className="bg-white border border-gray-200 rounded-xl p-4 space-y-4 shadow-sm">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div>
              <h3 className="text-base font-semibold text-gray-800">AI Summary</h3>
              <p className="text-sm text-gray-700 mt-1">{analysis.summary}</p>
            </div>
            <div className="flex flex-wrap gap-2 text-xs font-medium">
              <span className={`px-3 py-1 rounded-full ${sentimentColors[analysis.sentiment]}`}>
                Sentiment: {analysis.sentiment}
              </span>
              <span className={`px-3 py-1 rounded-full ${urgencyColors[analysis.urgency]}`}>
                Urgency: {analysis.urgency}
              </span>
              <span className="px-3 py-1 rounded-full bg-blue-50 text-blue-700">
                Duplicate hits: {totalDuplicates}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-gray-100 rounded-lg p-3 bg-gray-50">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Suggested Contact</h4>
              {analysis.contact ? (
                <ul className="text-sm text-gray-700 space-y-1">
                  {analysis.contact.name && <li>Name: {analysis.contact.name}</li>}
                  {analysis.contact.email && <li>Email: {analysis.contact.email}</li>}
                  {analysis.contact.phone && <li>Phone: {analysis.contact.phone}</li>}
                  {analysis.contact.jobTitle && <li>Role: {analysis.contact.jobTitle}</li>}
                  {analysis.contact.organization && <li>Org: {analysis.contact.organization}</li>}
                  <li className="text-xs text-gray-500">Confidence: {(analysis.contact.confidence * 100).toFixed(0)}%</li>
                </ul>
              ) : (
                <p className="text-xs text-gray-500">No contact details detected.</p>
              )}
            </div>
            <div className="border border-gray-100 rounded-lg p-3 bg-gray-50">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Suggested Deal / Lead</h4>
              {analysis.deal ? (
                <div className="text-sm text-gray-700 space-y-1">
                  {analysis.deal.title && <p>Deal: {analysis.deal.title}</p>}
                  {analysis.deal.value && analysis.deal.currency && (
                    <p>
                      Value: {analysis.deal.value} {analysis.deal.currency}
                    </p>
                  )}
                  {analysis.deal.expectedCloseDate && <p>Close Date: {analysis.deal.expectedCloseDate}</p>}
                  {analysis.deal.notes && <p className="text-xs text-gray-600">Notes: {analysis.deal.notes}</p>}
                  <p className="text-xs text-gray-500">Confidence: {(analysis.deal.confidence * 100).toFixed(0)}%</p>
                </div>
              ) : (
                <p className="text-xs text-gray-500">No deal suggestion.</p>
              )}
              {analysis.lead ? (
                <div className="text-xs text-gray-600 mt-2">
                  Lead: {analysis.lead.title} (confidence {(analysis.lead.confidence * 100).toFixed(0)}%)
                </div>
              ) : null}
            </div>
          </div>

          <div className="border border-gray-100 rounded-lg p-3">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Suggested Tasks</h4>
            {renderTasks(analysis.tasks)}
          </div>

          {analysis.keywords.length > 0 && (
            <div className="border border-gray-100 rounded-lg p-3">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Keywords</h4>
              <div className="flex flex-wrap gap-2 text-xs">
                {analysis.keywords.map((keyword) => (
                  <span key={keyword} className="px-3 py-1 rounded-full bg-gray-100 text-gray-700">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </section>
  );
};

export default EmailAnalyzer;
