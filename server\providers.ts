import OpenAI from 'openai';
import axios from 'axios';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { config, AIProvider, assertProviderConfig } from './config';
import { EmailAnalysisJsonSchema } from './types';

const SYSTEM_PROMPT = `You are an assistant that reads email messages and extracts CRM-ready insights for the sales team.

You must return a valid JSON object that strictly follows the provided schema. The JSON must include:
- A "summary" field with a concise summary of the email (required, non-empty string)
- A "sentiment" field with value 'positive', 'neutral', or 'negative' (required)
- An "urgency" field with value 'low', 'medium', or 'high' (required)
- Optional fields for "contact", "organization", "deal", "lead" - only include if relevant information is found
- A "tasks" array (can be empty if no tasks identified)
- A "keywords" array (can be empty if no keywords identified)

IMPORTANT: Do NOT return null values for any field. If information is not available, omit optional fields entirely. For required fields (summary, sentiment, urgency), you MUST provide meaningful values based on the email content.

Return strict JSON only. Do not include markdown, explanations, or any text outside the JSON structure.
`;

const buildUserPrompt = (subject: string, body: string) => `Email subject: ${subject}\n\nEmail body:\n${body}\n\n`;

const openAiClient = () => new OpenAI({ apiKey: config.openAiKey });

const geminiClient = () => new GoogleGenerativeAI(config.geminiKey!);

export const runProvider = async (provider: AIProvider, subject: string, body: string) => {
  console.log(`[DEBUG runProvider] AI_PROVIDER: '${provider}'`);
  console.log(`[DEBUG runProvider] process.env.GEMINI_API_KEY at runtime: '${process.env.GEMINI_API_KEY}'`);
  console.log(`[DEBUG runProvider] config.geminiKey at runtime: '${config.geminiKey}'`);
  assertProviderConfig(provider);
  const userPrompt = buildUserPrompt(subject, body);
  if (provider === 'openai') {
    return runOpenAI(userPrompt);
  }
  if (provider === 'gemini') {
    return runGemini(userPrompt);
  }
  return runOllama(userPrompt);
};

const runOpenAI = async (prompt: string) => {
  const client = openAiClient();
  const response = await client.chat.completions.create({
    model: config.openAiModel,
    messages: [
      { role: 'system', content: SYSTEM_PROMPT },
      { role: 'user', content: prompt },
    ],
    response_format: {
      type: 'json_object',
    },
    temperature: 0.2,
  });

  const content = response.choices[0]?.message?.content;
  if (!content) {
    throw new Error('OpenAI returned no content.');
  }
  return content;
};

const runGemini = async (prompt: string) => {
  const client = geminiClient();
  const model = client.getGenerativeModel({ model: config.geminiModel });
  
  // Create a more explicit prompt for Gemini
  const geminiPrompt = `${SYSTEM_PROMPT}

CRITICAL INSTRUCTIONS:
1. You MUST return a valid JSON object that follows this schema exactly.
2. The "summary" field MUST be a non-empty string summarizing the email.
3. The "sentiment" field MUST be either "positive", "neutral", or "negative".
4. The "urgency" field MUST be either "low", "medium", or "high".
5. For optional objects (contact, organization, deal, lead):
   - ONLY include the object if you find relevant information in the email
   - If you include an object, ONLY include the fields you have information for
   - NEVER include fields with null values - either provide a meaningful value or omit the field entirely
6. The "tasks" field MUST be an array of task objects. Each task object must have a "description" field (string).
   - If no tasks are identified, return an empty array: []
   - DO NOT return strings in the tasks array - only objects with "description" field
7. The "keywords" field MUST be an array of strings.
   - If no keywords are identified, return an empty array: []
8. Your response must be ONLY the JSON object, no additional text, explanations, or markdown.

EXAMPLE FORMAT:
{
  "summary": "Brief summary of the email",
  "sentiment": "neutral",
  "urgency": "medium",
  "contact": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "organization": {
    "name": "ABC Company"
  },
  "tasks": [
    {
      "description": "Follow up with the client"
    }
  ],
  "keywords": ["business", "opportunity"]
}

Email to analyze:
${prompt}`;
  
  const response = await model.generateContent({
    contents: [
      {
        role: 'user',
        parts: [
          {
            text: geminiPrompt,
          },
        ],
      },
    ],
    generationConfig: {
      temperature: 0.2,
      responseMimeType: 'application/json',
    },
  });
  const text = response.response?.text();
  if (!text) {
    throw new Error('Gemini returned no content.');
  }
  return text;
};

const runOllama = async (prompt: string) => {
  const payload = {
    model: config.ollamaModel,
    prompt: `${SYSTEM_PROMPT}\nSchema:${JSON.stringify(EmailAnalysisJsonSchema)}\n${prompt}\nReturn only JSON.`,
    options: {
      temperature: 0.1,
    },
  };
  const { data } = await axios.post(`${config.ollamaHost}/api/generate`, payload, {
    headers: { 'Content-Type': 'application/json' },
  });
  if (!data?.response) {
    throw new Error('Ollama returned no content.');
  }
  return data.response;
};
