import fs from 'node:fs';
import path from 'node:path';
import dotenv from 'dotenv';

const envFiles = ['.env', '.env.local'];
envFiles.forEach((file) => {
  const fullPath = path.resolve(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    dotenv.config({ path: fullPath, override: true });
  }
});

export type AIProvider = 'openai' | 'gemini' | 'ollama';

const required = (value: string | undefined, name: string) => {
  if (!value) {
    throw new Error(`${name} is required but was not provided.`);
  }
  return value;
};

const numberFromEnv = (value: string | undefined, fallback: number) => {
  if (!value) return fallback;
  const parsed = Number(value);
  return Number.isFinite(parsed) ? parsed : fallback;
};

export const config = {
  port: numberFromEnv(process.env.SERVER_PORT, 5175),
  aiProvider: (process.env.AI_PROVIDER as AIProvider | undefined) ?? 'gemini',
  openAiKey: process.env.OPENAI_API_KEY,
  openAiModel: process.env.OPENAI_MODEL ?? 'gpt-4.1-mini',
  geminiKey: process.env.GEMINI_API_KEY,
  geminiModel: process.env.GEMINI_MODEL ?? 'gemini-1.5-flash',
  ollamaModel: process.env.OLLAMA_MODEL ?? 'llama3.1:8b-instruct-q4_K_M',
  ollamaHost: process.env.OLLAMA_HOST ?? 'http://localhost:11434',
  pipedriveApiToken: process.env.PIPEDRIVE_API_TOKEN,
  pipedriveBaseUrl: process.env.PIPEDRIVE_BASE_URL ?? 'https://api.pipedrive.com/v1',
  pipedriveUserAgent: process.env.PIPEDRIVE_USER_AGENT ?? 'Pipedrive-Outlook-Sync/0.1.0',
};

export const assertPipedriveConfig = () => {
  if (!config.pipedriveApiToken) {
    throw new Error('PIPEDRIVE_API_TOKEN must be set to use the Pipedrive integration.');
  }
};

export const assertProviderConfig = (provider: AIProvider) => {
  if (provider === 'openai') {
    required(config.openAiKey, 'OPENAI_API_KEY');
  }
  if (provider === 'gemini') {
    required(config.geminiKey, 'GEMINI_API_KEY');
  }
};
