import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';

export const TaskSchema = z.object({
  description: z.string().min(1),
  dueDate: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
});

export const ContactSchema = z.object({
  name: z.string().nullable().optional(),
  email: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  jobTitle: z.string().nullable().optional(),
  organization: z.string().nullable().optional(),
  confidence: z.number().min(0).max(1).default(0.5),
});

export const OrganizationSchema = z.object({
  name: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  domain: z.string().nullable().optional(),
  confidence: z.number().min(0).max(1).default(0.5),
});

export const DealSchema = z.object({
  title: z.string().nullable().optional(),
  value: z.number().nullable().optional(),
  currency: z.string().nullable().optional(),
  expectedCloseDate: z.string().nullable().optional(),
  pipeline: z.string().nullable().optional(),
  stage: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
  confidence: z.number().min(0).max(1).default(0.5),
});

export const LeadSchema = z.object({
  title: z.string().nullable().optional(),
  source: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
  confidence: z.number().min(0).max(1).default(0.5),
});

export const EmailAnalysisSchema = z.object({
  summary: z.string().min(1),
  sentiment: z.enum(['positive', 'neutral', 'negative']).default('neutral'),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
  contact: ContactSchema.optional(),
  organization: OrganizationSchema.optional(),
  deal: DealSchema.optional(),
  lead: LeadSchema.optional(),
  tasks: z.array(TaskSchema).default([]),
  keywords: z.array(z.string()).default([]),
  rawExtractedFields: z.record(z.any()).optional(),
});

export const EmailAnalysisJsonSchema = zodToJsonSchema(EmailAnalysisSchema, {
  errorMessages: true,
  $refStrategy: 'none',
});

export type EmailAnalysis = z.infer<typeof EmailAnalysisSchema>;

export interface PipedriveSearchResult<T = any> {
  items: Array<{
    result_score?: number;
    item: T;
  }>;
}

export interface PipedrivePersonPayload {
  name: string;
  email?: string;
  phone?: string;
  org_id?: number;
  owner_id?: number;
  visible_to?: number;
  label?: number;
}

export interface PipedriveOrganizationPayload {
  name: string;
  address?: string;
  owner_id?: number;
  visible_to?: number;
}

export interface PipedriveDealPayload {
  title: string;
  value?: number;
  currency?: string;
  person_id?: number;
  org_id?: number;
  stage_id?: number;
  pipeline_id?: number;
  expected_close_date?: string;
  visible_to?: number;
  user_id?: number;
  label?: number;
  status?: string;
  add_time?: string;
  probability?: number;
  notes?: string;
}

export interface PipedriveLeadPayload {
  title: string;
  person_id?: number;
  org_id?: number;
  owner_id?: number;
  expected_close_date?: string;
  value?: number;
  currency?: string;
  label_ids?: number[];
  note?: string;
  source_name?: string;
}
