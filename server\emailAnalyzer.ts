import { z } from 'zod';
import { AIProvider, config } from './config';
import { EmailAnalysis, EmailAnalysisSchema } from './types';
import { runProvider } from './providers';

const InputSchema = z.object({
  subject: z.string().default('(no subject)'),
  body: z.string().min(1),
  provider: z.enum(['openai', 'gemini', 'ollama']).optional(),
});

export type AnalyzeEmailInput = z.infer<typeof InputSchema>;

export const analyzeEmail = async (input: AnalyzeEmailInput): Promise<EmailAnalysis> => {
  console.log(`[DEBUG analyzeEmail] Input received: ${JSON.stringify(input)}`);
  try {
    const { subject, body, provider } = InputSchema.parse(input);
    const providerToUse: AIProvider = provider ?? config.aiProvider;
    console.log(`[DEBUG analyzeEmail] Using provider: ${providerToUse}`);
    const raw = await run<PERSON>rovider(providerToUse, subject, body);
    console.log(`[DEBUG analyzeEmail] Raw response from AI: ${raw}`);
    try {
      const json = JSON.parse(raw);
      console.log(`[DEBUG analyzeEmail] Parsed JSON: ${JSON.stringify(json, null, 2)}`);
      const parsed = EmailAnalysisSchema.safeParse(json);
      if (!parsed.success) {
        console.log(`[DEBUG analyzeEmail] Schema validation error: ${JSON.stringify(parsed.error, null, 2)}`);
        throw parsed.error;
      }
      console.log(`[DEBUG analyzeEmail] Successfully parsed response`);
      return parsed.data;
    } catch (error: any) {
      console.log(`[DEBUG analyzeEmail] Error parsing response: ${error?.message}`);
      const message = error?.message ?? 'Failed to parse model response';
      throw new Error(`Unable to parse AI output: ${message}`);
    }
  } catch (error: any) {
    console.log(`[DEBUG analyzeEmail] Input validation or other error: ${error?.message}`);
    throw error;
  }
};
