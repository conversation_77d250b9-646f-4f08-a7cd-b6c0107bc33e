import React, { useEffect, useMemo, useState } from 'react';
import { createDeal } from '../services/pipedriveService';
import Input from './common/Input';
import Button from './common/Button';
import {
  EmailDealSuggestion,
  FormProps,
  LinkedOrganization,
  LinkedPerson,
  PipedriveDeal,
  PipedriveSearchResult,
} from '../types';

interface DealFormProps extends FormProps {
  suggestion?: EmailDealSuggestion;
  linkedPerson: LinkedPerson | null;
  linkedOrganization: LinkedOrganization | null;
  duplicates?: PipedriveSearchResult<PipedriveDeal>;
}

const DealForm: React.FC<DealFormProps> = ({ showToast, suggestion, linkedPerson, linkedOrganization, duplicates }) => {
  const [title, setTitle] = useState('');
  const [value, setValue] = useState('');
  const [currency, setCurrency] = useState('USD');
  const [expectedCloseDate, setExpectedCloseDate] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedExistingId, setSelectedExistingId] = useState<number | null>(null);

  useEffect(() => {
    if (!suggestion) return;
    setTitle(suggestion.title ?? '');
    setCurrency(suggestion.currency ?? 'USD');
    setNotes(suggestion.notes ?? '');
    if (suggestion.value !== undefined && suggestion.value !== null) {
      setValue(String(suggestion.value));
    }
    setExpectedCloseDate(suggestion.expectedCloseDate ?? '');
  }, [suggestion]);

  useEffect(() => {
    if (selectedExistingId || !duplicates?.items?.length) return;
    const [first] = duplicates.items;
    if (first?.item?.id) {
      setSelectedExistingId(first.item.id);
    }
  }, [duplicates, selectedExistingId]);

  const selectedExisting = useMemo(() => {
    if (!duplicates || !selectedExistingId) return null;
    return duplicates.items.find((entry) => entry.item.id === selectedExistingId)?.item ?? null;
  }, [duplicates, selectedExistingId]);

  const resetForm = () => {
    setTitle('');
    setValue('');
    setCurrency('USD');
    setExpectedCloseDate('');
    setNotes('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title) {
      showToast('Deal title is required.', 'error');
      return;
    }
    setIsLoading(true);
    try {
      const payload = {
        title,
        value: value ? Number(value) : undefined,
        currency,
        expected_close_date: expectedCloseDate || undefined,
        notes: notes || undefined,
        person_id: linkedPerson?.id,
        org_id: linkedOrganization?.id,
      };
      await createDeal(payload);
      showToast('Deal created in Pipedrive.', 'success');
      resetForm();
    } catch (error: any) {
      showToast(error?.message ?? 'Failed to create deal.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-700">Create Deal</h2>
        <Input
          id="deal-title"
          label="Deal Title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="e.g., Acme Corp Website Redesign"
          required
        />
        <div className="flex space-x-4">
          <div className="flex-grow">
            <Input
              id="deal-value"
              label="Value"
              type="number"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="e.g., 5000"
              min="0"
            />
          </div>
          <div className="w-1/3">
            <label htmlFor="deal-currency" className="block text-sm font-medium text-gray-700 mb-1">
              Currency
            </label>
            <select
              id="deal-currency"
              value={currency}
              onChange={(e) => setCurrency(e.target.value)}
              className="block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm transition-colors"
            >
              <option>USD</option>
              <option>EUR</option>
              <option>GBP</option>
            </select>
          </div>
        </div>
        <Input
          id="deal-expected-close"
          label="Expected Close Date"
          type="date"
          value={expectedCloseDate}
          onChange={(e) => setExpectedCloseDate(e.target.value)}
        />
        <div>
          <label htmlFor="deal-notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <textarea
            id="deal-notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={4}
            className="block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm transition-colors"
            placeholder="Include summary, next steps or any details for this deal"
          />
        </div>
        {linkedPerson && (
          <p className="text-xs text-gray-600">Linked contact: {linkedPerson.name}</p>
        )}
        {linkedOrganization && (
          <p className="text-xs text-gray-600">Linked organization: {linkedOrganization.name}</p>
        )}
        <div className="pt-2">
          <Button type="submit" isLoading={isLoading}>
            Create Deal
          </Button>
        </div>
      </form>

      {duplicates?.items?.length ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Possible Existing Deals</h3>
          <div className="space-y-3">
            {duplicates.items.map(({ item, result_score }) => (
              <label
                key={item.id}
                className={`block border rounded-lg p-3 cursor-pointer transition-colors ${
                  selectedExistingId === item.id ? 'border-green-400 bg-green-50' : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    name="deal-duplicate"
                    className="mt-1"
                    value={item.id}
                    checked={selectedExistingId === item.id}
                    onChange={() => setSelectedExistingId(item.id)}
                  />
                  <div className="flex-1 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-800">{item.title}</span>
                      {typeof result_score === 'number' && (
                        <span className="text-xs text-gray-500">Score: {result_score.toFixed(2)}</span>
                      )}
                    </div>
                    {item.value !== undefined && item.currency && (
                      <p className="text-xs text-gray-600">
                        Value: {item.value} {item.currency}
                      </p>
                    )}
                  </div>
                </div>
              </label>
            ))}
          </div>
          {selectedExisting && (
            <p className="mt-3 text-xs text-gray-600">
              Consider updating the existing deal "{selectedExisting.title}" instead of creating a duplicate.
            </p>
          )}
        </div>
      ) : (
        <div className="text-xs text-gray-500">No similar deals found.</div>
      )}
    </div>
  );
};

export default DealForm;
