import React, { useEffect, useMemo, useState } from 'react';
import { createLead } from '../services/pipedriveService';
import Input from './common/Input';
import Button from './common/Button';
import {
  EmailLeadSuggestion,
  FormProps,
  LinkedOrganization,
  LinkedPerson,
  PipedriveLead,
  PipedriveSearchResult,
} from '../types';

interface LeadFormProps extends FormProps {
  suggestion?: EmailLeadSuggestion;
  linkedPerson: LinkedPerson | null;
  linkedOrganization: LinkedOrganization | null;
  duplicates?: PipedriveSearchResult<PipedriveLead>;
}

const LeadForm: React.FC<LeadFormProps> = ({ showToast, suggestion, linkedPerson, linkedOrganization, duplicates }) => {
  const [title, setTitle] = useState('');
  const [source, setSource] = useState('');
  const [note, setNote] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedExistingId, setSelectedExistingId] = useState<string | null>(null);

  useEffect(() => {
    if (!suggestion) return;
    setTitle(suggestion.title ?? '');
    setSource(suggestion.source ?? '');
    setNote(suggestion.notes ?? '');
  }, [suggestion]);

  useEffect(() => {
    if (selectedExistingId || !duplicates?.items?.length) return;
    const [first] = duplicates.items;
    if (first?.item?.id) {
      setSelectedExistingId(String(first.item.id));
    }
  }, [duplicates, selectedExistingId]);

  const selectedExisting = useMemo(() => {
    if (!duplicates || !selectedExistingId) return null;
    return duplicates.items.find((entry) => String(entry.item.id) === selectedExistingId)?.item ?? null;
  }, [duplicates, selectedExistingId]);

  const resetForm = () => {
    setTitle('');
    setSource('');
    setNote('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title) {
      showToast('Lead title is required.', 'error');
      return;
    }
    setIsLoading(true);
    try {
      await createLead({
        title,
        person_id: linkedPerson?.id,
        org_id: linkedOrganization?.id,
        source_name: source || undefined,
        note: note || undefined,
      });
      showToast('Lead created in Pipedrive.', 'success');
      resetForm();
    } catch (error: any) {
      showToast(error?.message ?? 'Failed to create lead.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-700">Create Lead</h2>
        <Input
          id="lead-title"
          label="Lead Title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="e.g., Inquiry from Globex Corporation"
          required
        />
        <Input
          id="lead-source"
          label="Source"
          type="text"
          value={source}
          onChange={(e) => setSource(e.target.value)}
          placeholder="e.g., Cold Email, Website, Referral"
        />
        <div>
          <label htmlFor="lead-notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <textarea
            id="lead-notes"
            value={note}
            onChange={(e) => setNote(e.target.value)}
            rows={4}
            className="block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm transition-colors"
            placeholder="Capture intent, timeline, or key info from the email"
          />
        </div>
        {linkedPerson && <p className="text-xs text-gray-600">Linked contact: {linkedPerson.name}</p>}
        {linkedOrganization && <p className="text-xs text-gray-600">Linked organization: {linkedOrganization.name}</p>}
        <div className="pt-2">
          <Button type="submit" isLoading={isLoading}>
            Create Lead
          </Button>
        </div>
      </form>

      {duplicates?.items?.length ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Existing Leads</h3>
          <div className="space-y-3">
            {duplicates.items.map(({ item, result_score }) => (
              <label
                key={item.id}
                className={`block border rounded-lg p-3 cursor-pointer transition-colors ${
                  selectedExistingId === String(item.id) ? 'border-green-400 bg-green-50' : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    name="lead-duplicate"
                    className="mt-1"
                    value={String(item.id)}
                    checked={selectedExistingId === String(item.id)}
                    onChange={() => setSelectedExistingId(String(item.id))}
                  />
                  <div className="flex-1 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-800">{item.title}</span>
                      {typeof result_score === 'number' && (
                        <span className="text-xs text-gray-500">Score: {result_score.toFixed(2)}</span>
                      )}
                    </div>
                  </div>
                </div>
              </label>
            ))}
          </div>
          {selectedExisting && (
            <p className="mt-3 text-xs text-gray-600">
              An existing lead "{selectedExisting.title}" looks similar. You may want to update it instead.
            </p>
          )}
        </div>
      ) : (
        <div className="text-xs text-gray-500">No similar leads found.</div>
      )}
    </div>
  );
};

export default LeadForm;
