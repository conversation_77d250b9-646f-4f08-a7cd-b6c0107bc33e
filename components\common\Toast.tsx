
import React, { useEffect, useState } from 'react';
import { ToastMessage } from '../../types';

interface ToastProps extends ToastMessage {
  onClose: () => void;
}

const SuccessIcon = () => (
    <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
);

const ErrorIcon = () => (
    <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
);

const Toast: React.FC<ToastProps> = ({ message, type, onClose }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(true);
    const timer = setTimeout(() => {
      setVisible(false);
      // Allow time for fade-out animation before calling onClose
      setTimeout(onClose, 300);
    }, 2700);

    return () => clearTimeout(timer);
  }, [message, type, onClose]);
  
  const baseClasses = 'fixed bottom-5 right-5 w-full max-w-sm p-4 rounded-lg shadow-lg flex items-center space-x-4 transition-all duration-300 ease-in-out z-50';
  const typeClasses = {
    success: 'bg-green-100 border border-green-300 text-green-800',
    error: 'bg-red-100 border border-red-300 text-red-800',
  };
  const visibilityClasses = visible ? 'transform translate-y-0 opacity-100' : 'transform translate-y-10 opacity-0';

  return (
    <div className={`${baseClasses} ${typeClasses[type]} ${visibilityClasses}`}>
        {type === 'success' ? <SuccessIcon /> : <ErrorIcon />}
        <span className="flex-grow">{message}</span>
    </div>
  );
};

export default Toast;
