import axios from 'axios';
import { config, assertPipedriveConfig } from './config';
import {
  PipedriveDealPayload,
  PipedriveLeadPayload,
  PipedriveOrganizationPayload,
  PipedrivePersonPayload,
  PipedriveSearchResult,
} from './types';

const client = axios.create({
  baseURL: config.pipedriveBaseUrl,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': config.pipedriveUserAgent,
  },
  params: {
    api_token: config.pipedriveApiToken,
  },
});

client.interceptors.request.use((request) => {
  if (!config.pipedriveApiToken) {
    throw new Error('Pipedrive API token is missing. Set PIPEDRIVE_API_TOKEN.');
  }
  return request;
});

const get = async <T>(url: string, params?: Record<string, any>) => {
  assertPipedriveConfig();
  const { data } = await client.get<T>(url, { params });
  return data;
};

const post = async <T>(url: string, payload: any) => {
  assertPipedriveConfig();
  const { data } = await client.post<T>(url, payload);
  return data;
};

export const searchPersons = async (term: string) => {
  const data = await get<{ data: PipedriveSearchResult }>(`/persons/search`, {
    term,
    fields: 'name,email,phone',
    exact_match: false,
    include_fields: 'emails,phones,organization',
    limit: 10,
  });
  return data.data;
};

export const searchOrganizations = async (term: string) => {
  const data = await get<{ data: PipedriveSearchResult }>(`/organizations/search`, {
    term,
    exact_match: false,
    limit: 10,
  });
  return data.data;
};

export const searchDeals = async (term: string) => {
  const data = await get<{ data: PipedriveSearchResult }>(`/deals/search`, {
    term,
    exact_match: false,
    limit: 10,
  });
  return data.data;
};

export const searchLeads = async (term: string) => {
  const data = await get<{ data: PipedriveSearchResult }>(`/leads/search`, {
    term,
    exact_match: false,
    limit: 10,
  });
  return data.data;
};

export const createPerson = async (payload: PipedrivePersonPayload) => {
  const response = await post(`/persons`, payload);
  return response;
};

export const createOrganization = async (payload: PipedriveOrganizationPayload) => {
  const response = await post(`/organizations`, payload);
  return response;
};

export const createDeal = async (payload: PipedriveDealPayload) => {
  const response = await post(`/deals`, payload);
  return response;
};

export const createLead = async (payload: PipedriveLeadPayload) => {
  const response = await post(`/leads`, payload);
  return response;
};

export const mergePersons = async (primaryPersonId: number, mergePersonId: number) => {
  const response = await post(`/persons/${primaryPersonId}/merge`, {
    merge_with_id: mergePersonId,
  });
  return response;
};

