import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EmailAnalysis,
  EmailAnalyzePayload,
  LinkedOrganization,
  LinkedPerson,
  Tab,
  ToastMessage,
} from './types';
import ContactForm from './components/ContactForm';
import DealForm from './components/DealForm';
import LeadForm from './components/LeadForm';
import Toast from './components/common/Toast';
import EmailAnalyzer from './components/EmailAnalyzer';
import OrganizationManager from './components/OrganizationManager';
import { analyzeEmail } from './services/emailService';

const PipedriveLogo = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
    <path d="M18.38 5.62C15.69 2.93 11.4 2.45 8.24 4.5L13.5 9.76L9.76 13.5L4.5 8.24C2.45 11.4 2.93 15.69 5.62 18.38C8.61 21.37 13.39 21.37 16.38 18.38C19.37 15.39 19.37 10.61 18.38 7.62L19.45 6.55C20.03 5.97 20.03 5.03 19.45 4.45L17.55 2.55C16.97 1.97 16.03 1.97 15.45 2.55L12 6L18.38 5.62ZM12 18C10.34 18 9 16.66 9 15C9 13.34 10.34 12 12 12C13.66 12 15 13.34 15 15C15 16.66 13.66 18 12 18Z" fill="#22C55E"/>
  </svg>
);

const UserIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
  </svg>
);

const DealIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
    <path d="M8.433 7.418c.158-.103.346-.196.552-.257l.062-.018a2.25 2.25 0 012.166 0l.062.018c.206.06.394.154.552.257 1.121.732 1.121 2.424 0 3.156-.158.103-.346.196-.552.257l-.062.018a2.25 2.25 0 01-2.166 0l-.062-.018a3.375 3.375 0 01-.552-.257c-1.121-.732-1.121-2.424 0-3.156zM15.75 7.5c.414 0 .75.336.75.75v3c0 .414-.336.75-.75.75h-.75v1.5a.75.75 0 01-1.5 0v-1.5h-1.5a.75.75 0 010-1.5h1.5v-1.5a.75.75 0 011.5 0v1.5h.75a.75.75 0 00.75-.75v-3a.75.75 0 00-.75-.75H15z" />
    <path d="M2.25 9.75A.75.75 0 003 9h.75v1.5a.75.75 0 001.5 0v-1.5h1.5a.75.75 0 000-1.5h-1.5v-1.5a.75.75 0 00-1.5 0v1.5H3a.75.75 0 00-.75.75v3z" />
  </svg>
);

const LeadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
  </svg>
);

const defaultTab = Tab.CONTACT;

export default function App() {
  const [activeTab, setActiveTab] = useState<Tab>(defaultTab);
  const [toast, setToast] = useState<ToastMessage | null>(null);
  const [analysis, setAnalysis] = useState<EmailAnalysis | null>(null);
  const [duplicates, setDuplicates] = useState<DuplicateMatches | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [linkedPerson, setLinkedPerson] = useState<LinkedPerson | null>(null);
  const [linkedOrganization, setLinkedOrganization] = useState<LinkedOrganization | null>(null);

  const showToast = useCallback((message: string, type: 'success' | 'error') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 4000);
  }, []);

  const handleAnalyzeEmail = useCallback(
    async (payload: EmailAnalyzePayload) => {
      setIsAnalyzing(true);
      try {
        const result = await analyzeEmail(payload);
        setAnalysis(result.analysis);
        setDuplicates(result.duplicates);
        setLinkedPerson(null);
        setLinkedOrganization(null);
        showToast('Email analysed successfully.', 'success');
      } catch (error: any) {
        showToast(error?.message ?? 'Failed to analyse email.', 'error');
      } finally {
        setIsAnalyzing(false);
      }
    },
    [showToast],
  );

  const tabPillClass = useCallback(
    (tab: Tab) =>
      `flex-1 px-4 py-3 text-sm font-medium text-center rounded-md cursor-pointer transition-all duration-200 ease-in-out flex items-center justify-center ${
        activeTab === tab ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:bg-gray-200'
      }`,
    [activeTab],
  );

  const renderContent = useMemo(() => {
    switch (activeTab) {
      case Tab.CONTACT:
        return (
          <ContactForm
            showToast={showToast}
            suggestion={analysis?.contact}
            duplicates={duplicates?.persons}
            linkedPerson={linkedPerson}
            linkedOrganization={linkedOrganization}
            onPersonLinked={setLinkedPerson}
          />
        );
      case Tab.DEAL:
        return (
          <DealForm
            showToast={showToast}
            suggestion={analysis?.deal}
            linkedPerson={linkedPerson}
            linkedOrganization={linkedOrganization}
            duplicates={duplicates?.deals}
          />
        );
      case Tab.LEAD:
        return (
          <LeadForm
            showToast={showToast}
            suggestion={analysis?.lead}
            linkedPerson={linkedPerson}
            linkedOrganization={linkedOrganization}
            duplicates={duplicates?.leads}
          />
        );
      default:
        return null;
    }
  }, [activeTab, analysis, duplicates, linkedOrganization, linkedPerson, showToast]);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 font-sans">
      <div className="w-full max-w-4xl bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-200">
        <header className="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center">
            <PipedriveLogo />
            <h1 className="text-xl font-bold text-gray-800">Pipedrive Sync</h1>
          </div>
        </header>

        <main className="p-6 space-y-6 bg-gray-50">
          <EmailAnalyzer
            onAnalyze={handleAnalyzeEmail}
            isAnalyzing={isAnalyzing}
            analysis={analysis}
            duplicates={duplicates}
          />

          <OrganizationManager
            suggestion={analysis?.organization}
            duplicates={duplicates?.organizations}
            linkedOrganization={linkedOrganization}
            onOrganizationLinked={setLinkedOrganization}
            showToast={showToast}
          />

          <section className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
            <nav className="p-2 bg-gray-100 rounded-lg flex space-x-2 mb-4">
              <button onClick={() => setActiveTab(Tab.CONTACT)} className={tabPillClass(Tab.CONTACT)}>
                <UserIcon /> Contact
              </button>
              <button onClick={() => setActiveTab(Tab.DEAL)} className={tabPillClass(Tab.DEAL)}>
                <DealIcon /> Deal
              </button>
              <button onClick={() => setActiveTab(Tab.LEAD)} className={tabPillClass(Tab.LEAD)}>
                <LeadIcon /> Lead
              </button>
            </nav>

            <div className="p-2">{renderContent}</div>
          </section>
        </main>

        {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
      </div>
    </div>
  );
}
