const API_BASE_URL = (import.meta.env.VITE_SERVER_URL || 'http://localhost:5175').replace(/\/$/, '');

interface RequestOptions extends RequestInit {
  skipJson?: boolean;
}

const buildHeaders = (headers?: HeadersInit) => {
  const base: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (!headers) return base;
  return { ...base, ...(headers as Record<string, string>) };
};

const parseError = async (response: Response) => {
  try {
    const data = await response.json();
    if (data?.error) {
      return data.error;
    }
    return JSON.stringify(data);
  } catch (error) {
    return response.statusText || 'Unknown error';
  }
};

export const apiRequest = async <T>(path: string, options: RequestOptions = {}): Promise<T> => {
  const { skipJson, headers, body, ...rest } = options;
  const finalHeaders = buildHeaders(headers);
  const payload = typeof body === 'string' ? body : body ? JSON.stringify(body) : undefined;
  const response = await fetch(`${API_BASE_URL}${path}`, {
    headers: finalHeaders,
    body: payload,
    ...rest,
  });

  if (!response.ok) {
    const message = await parseError(response);
    throw new Error(message);
  }

  if (skipJson || response.status === 204) {
    return undefined as T;
  }

  return (await response.json()) as T;
};

export const buildQueryString = (params: Record<string, string | number | undefined>) => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null) return;
    searchParams.append(key, String(value));
  });
  const query = searchParams.toString();
  return query ? `?${query}` : '';
};

export type ApiResponse<T> = { data: T };
